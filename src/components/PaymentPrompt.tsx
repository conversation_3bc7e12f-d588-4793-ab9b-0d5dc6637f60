'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Crown, CreditCard, Smartphone, Heart, Users, MessageCircle, Star, ArrowRight, X } from 'lucide-react'

interface PaymentPromptProps {
  isOpen: boolean
  onClose: () => void
  feature?: string
}

export default function PaymentPrompt({ isOpen, onClose, feature = "matches" }: PaymentPromptProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  if (!isOpen) return null

  const handlePaymentRedirect = () => {
    setIsLoading(true)
    router.push('/upgrade')
  }

  const features = [
    {
      icon: <Users className="w-5 h-5 text-pink-500" />,
      title: "Unlimited Matches",
      description: "Connect with unlimited potential partners"
    },
    {
      icon: <MessageCircle className="w-5 h-5 text-blue-500" />,
      title: "Unlimited Messaging",
      description: "Chat freely with all your matches"
    },
    {
      icon: <Star className="w-5 h-5 text-yellow-500" />,
      title: "Advanced Filters",
      description: "Find exactly who you're looking for"
    },
    {
      icon: <Heart className="w-5 h-5 text-red-500" />,
      title: "Priority Support",
      description: "Get help when you need it most"
    }
  ]

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl md:rounded-3xl shadow-2xl w-full max-w-md md:max-w-5xl max-h-[95vh] overflow-y-auto mx-auto">
        {/* Header */}
        <div className="relative bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-600 text-white p-6 md:p-8 rounded-t-2xl md:rounded-t-3xl">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 md:top-6 md:right-6 text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full"
          >
            <X className="w-5 h-5 md:w-6 md:h-6" />
          </button>

          <div className="text-center max-w-2xl mx-auto">
            <div className="w-16 h-16 md:w-20 md:h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-6">
              <Crown className="w-8 h-8 md:w-10 md:h-10 text-yellow-300" />
            </div>
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3">Unlock Premium Features</h2>
            <p className="text-white/90 text-base md:text-lg">
              {feature === "matches"
                ? "To connect with matches, you need an active subscription"
                : `To access ${feature}, you need an active subscription`
              }
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 md:p-8">
          <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Features Section */}
            <div className="lg:col-span-2 order-2 lg:order-1">
              <h3 className="text-xl font-bold text-gray-900 mb-6">What you'll get with Premium:</h3>
              <div className="grid md:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                    <div className="flex-shrink-0 w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-sm">
                      {feature.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 mb-1">{feature.title}</div>
                      <div className="text-sm text-gray-600">{feature.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Pricing & Payment Section */}
            <div className="lg:col-span-1 order-1 lg:order-2">
              <div className="bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 rounded-2xl p-6 border border-gray-100 lg:sticky lg:top-4">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-gray-900 mb-2">KSh 1,200</div>
                  <div className="text-gray-600 mb-3">120 days of premium access</div>
                  <div className="inline-block bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium">
                    Only KSh 10 per day!
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-4 text-center">Choose Payment Method:</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-xl hover:border-pink-400 hover:bg-white transition-all cursor-pointer group">
                      <Smartphone className="w-6 h-6 text-green-600 mr-3 group-hover:scale-110 transition-transform" />
                      <span className="font-medium">M-Pesa Payment</span>
                    </div>
                    <div className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-xl hover:border-blue-400 hover:bg-white transition-all cursor-pointer group">
                      <CreditCard className="w-6 h-6 text-blue-600 mr-3 group-hover:scale-110 transition-transform" />
                      <span className="font-medium">Credit/Debit Card</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={handlePaymentRedirect}
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-pink-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50 shadow-lg hover:shadow-xl"
                  >
                    {isLoading ? (
                      'Redirecting...'
                    ) : (
                      <>
                        Get Premium Access
                        <ArrowRight className="w-5 h-5" />
                      </>
                    )}
                  </button>

                  <button
                    onClick={onClose}
                    className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-medium hover:bg-gray-200 transition-colors"
                  >
                    Maybe Later
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-8 pt-6 border-t border-gray-100">
            <div className="text-center">
              <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="text-green-500">🔒</span>
                  <span>Secure Payment</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-blue-500">💳</span>
                  <span>Instant Activation</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-purple-500">📱</span>
                  <span>Mobile Friendly</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
