'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Crown, CreditCard, Smartphone, Heart, Users, MessageCircle, Star, ArrowRight, X } from 'lucide-react'

interface PaymentPromptProps {
  isOpen: boolean
  onClose: () => void
  feature?: string
}

export default function PaymentPrompt({ isOpen, onClose, feature = "matches" }: PaymentPromptProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  if (!isOpen) return null

  const handlePaymentRedirect = () => {
    setIsLoading(true)
    router.push('/upgrade')
  }

  const features = [
    {
      icon: <Users className="w-5 h-5 text-gray-600" />,
      title: "Unlimited Matches",
      description: "Connect with unlimited potential partners"
    },
    {
      icon: <MessageCircle className="w-5 h-5 text-gray-600" />,
      title: "Unlimited Messaging",
      description: "Chat freely with all your matches"
    },
    {
      icon: <Star className="w-5 h-5 text-gray-600" />,
      title: "Advanced Filters",
      description: "Find exactly who you're looking for"
    },
    {
      icon: <Heart className="w-5 h-5 text-gray-600" />,
      title: "Priority Support",
      description: "Get help when you need it most"
    }
  ]

  return (
    <div className="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-md md:max-w-4xl max-h-[95vh] overflow-y-auto mx-auto border border-gray-100">
        {/* Header */}
        <div className="relative bg-white border-b border-gray-100 p-6 md:p-8">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 md:top-6 md:right-6 text-gray-400 hover:text-gray-600 transition-colors p-1 hover:bg-gray-50 rounded-lg"
          >
            <X className="w-5 h-5 md:w-6 md:h-6" />
          </button>

          <div className="text-center max-w-2xl mx-auto">
            <div className="w-12 h-12 md:w-14 md:h-14 bg-gray-50 rounded-xl flex items-center justify-center mx-auto mb-4 md:mb-6">
              <Crown className="w-6 h-6 md:w-7 md:h-7 text-gray-700" />
            </div>
            <h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2 md:mb-3">Unlock Premium Features</h2>
            <p className="text-gray-600 text-sm md:text-base">
              {feature === "matches"
                ? "To connect with matches, you need an active subscription"
                : `To access ${feature}, you need an active subscription`
              }
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 md:p-8">
          <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Features Section */}
            <div className="lg:col-span-2 order-2 lg:order-1">
              <h3 className="text-lg font-medium text-gray-900 mb-6">What you'll get with Premium:</h3>
              <div className="grid md:grid-cols-2 gap-3">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 border border-gray-100 rounded-lg hover:border-gray-200 transition-colors">
                    <div className="flex-shrink-0 w-10 h-10 bg-gray-50 rounded-lg flex items-center justify-center">
                      {feature.icon}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">{feature.title}</div>
                      <div className="text-sm text-gray-600">{feature.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Pricing & Payment Section */}
            <div className="lg:col-span-1 order-1 lg:order-2">
              <div className="bg-gray-50 rounded-lg p-6 border border-gray-100 lg:sticky lg:top-4">
                <div className="text-center mb-6">
                  <div className="text-3xl font-semibold text-gray-900 mb-2">KSh 1,200</div>
                  <div className="text-gray-600 mb-3">120 days of premium access</div>
                  <div className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-md text-sm font-medium">
                    Only KSh 10 per day
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-4 text-center">Choose Payment Method:</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-center p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-white transition-all cursor-pointer">
                      <Smartphone className="w-5 h-5 text-gray-600 mr-3" />
                      <span className="font-medium text-gray-900">M-Pesa Payment</span>
                    </div>
                    <div className="flex items-center justify-center p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-white transition-all cursor-pointer">
                      <CreditCard className="w-5 h-5 text-gray-600 mr-3" />
                      <span className="font-medium text-gray-900">Credit/Debit Card</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={handlePaymentRedirect}
                    disabled={isLoading}
                    className="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                  >
                    {isLoading ? (
                      'Redirecting...'
                    ) : (
                      <>
                        Get Premium Access
                        <ArrowRight className="w-4 h-4" />
                      </>
                    )}
                  </button>

                  <button
                    onClick={onClose}
                    className="w-full bg-white text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors border border-gray-200"
                  >
                    Maybe Later
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-8 pt-6 border-t border-gray-100">
            <div className="text-center">
              <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="text-green-500">🔒</span>
                  <span>Secure Payment</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-blue-500">💳</span>
                  <span>Instant Activation</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-purple-500">📱</span>
                  <span>Mobile Friendly</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
