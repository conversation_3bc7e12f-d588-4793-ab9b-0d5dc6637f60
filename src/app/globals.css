@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

input:disabled,
textarea:disabled,
select:disabled,
input[readonly],
textarea[readonly] {
  color: #111827 !important;           /* <PERSON>l<PERSON>'s text-gray-900 */
  background-color: #f3f4f6 !important; /* Tailwind's bg-gray-100 */
  border-color: #d1d5db !important;     /* <PERSON>lwind's border-gray-300 */
  font-weight: 500 !important;          /* font-medium */
  opacity: 1 !important;
}

option:disabled {
  color: #6b7280 !important; /* <PERSON><PERSON><PERSON>'s text-gray-500 or darker */
  opacity: 1 !important;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
